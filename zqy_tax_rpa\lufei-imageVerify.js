const Untils = require('./untils')
const request = require('sync-request'); //默认同步请求
const pbottleRPA = require('./pbottleRPA')
const xlsx = require("node-xlsx");
const fs = require('fs')
const php = require("php2javascript");
const {
	FormData
} = require('sync-request');
const ExcelJS = require('exceljs');
const path = require('path');
const global_access_token = 'Basic c2FiZXI6c2FiZXJfc2VjcmV0';
const untils = new Untils(global_access_token);

main()

async function main() {
    let res = await getVerifyImage()
    // console.log('imagecode[0]',res.slider_back)
    // console.log('imagecode[1]',res.slider_block)
    // await check_verifyImage()
}

async function getVerifyImage(){
    await pbottleRPA.browserCMD_click(`button span:contains(登录)`)
    const top  = await pbottleRPA.browserCMD_css('div#slideVerify > div:nth-child(1) ') 
    console.log(top)
    let varifyImage = pbottleRPA.browserCMD_attr('div#slideVerify img', 'src')
    let slider_back = JSON.parse(varifyImage)[0]
    let slider_block = JSON.parse(varifyImage)[1]
    return {slider_back,slider_block}
}

async function check_verifyImage (){
    let slider  = await untils.waitImage('/input/1920/slider.png')
    console.log('滑块验证',slider)
    pbottleRPA.moveMouseSmooth(slider.x,slider.y)
    pbottleRPA.mouseLeftDragTo(slider.x + 200,slider.y)
    let slider_check = await untils.existImage2('/input/1920/loginBT.png')
    console.log('slider_check',slider_check)
    if(!slider_check){
        console.log('验证失败')
        pbottleRPA.moveMouseSmooth(200,200)
        pbottleRPA.mouseClick()
        pbottleRPA.sleep(1000)
        main()
    }else{
        console.log('验证成功')
    }
}

function checkVerifyImage(){
    let ewm = pbottleRPA.browserCMD_attr('div#qrcodeDivE img', 'src')
    console.log('二维码',ewm)
}